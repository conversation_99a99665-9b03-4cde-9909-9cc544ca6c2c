<template>
    <a-modal
    v-model="visible"
    title="政策详情"
    @ok="handleOk"
    :footer="null"
    :centered="true"
    width='70%'
    :closable='true'
    :destroyOnClose='true'
    >
    <!-- <div class="close" @click="dilog"></div> -->
    <div class="content">
      <pdf
       v-for="i in numPages"
      :key="i"
       :page="i"
      ref="pdf"
      :src="article">
      </pdf>
    </div>
    </a-modal>
</template>

<script>
import pdf from 'vue-pdf'
export default {
  components: { pdf },
  data() {
    return {
      // numPages: 0,
      src: null,
      numPages: undefined,
      visible: false,
      article: '',
      bt: ''
    }
  },
  mounted() {
  },
  methods: {
    // async  initpdf() {
    //   const pdfa = pdf.createLoadingTask(this.article)
    //   const pdfb = await pdfa.promise
    //   console.log(pdfb)
    //   this.numPages = pdfb.numPages
    //   console.log(this.numPages)
    // },
    async show(data) {
      const pdfa = pdf.createLoadingTask(data.article)
      console.log(pdfa)
      const pdfb = await pdfa.promise
      console.log(pdfb)
      this.numPages = pdfb.numPages
      console.log(this.numPages)

      this.visible = true
      this.bt = data.bt
      this.article = data.article
    },
    handleOk() {
      this.visible = false
    },
    dilog() {
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
   /deep/ .ant-modal-content{
      height: 70vh;//弹窗的高度在这里修改
      background: #fff;
      overflow: hidden;
      .ant-modal-close{
      opacity: 0;
    }

  }
   /deep/ .ant-modal-header {
      padding: 14px 17px 14px 19px;
      background: url('./images/tk.png') no-repeat center;
      background-size:100% 100% ;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #FFFFFF;
      height: 44px;
      display: flex;
      align-items: center;
      position: relative;
      .ant-modal-title {
      color: #fff;
      font-weight: 500;
      font-size: 16px;
  }
      &::after{
        display: block;
        content: '';
        width: 17px;
        height: 17px;
        background: url('./images/close.png') no-repeat center;
        background-size:100% 100% ;
        position: absolute;
        right: 14px;
        top: 14px;
        cursor: pointer;

      }

  }
   /deep/ .ant-modal-body {
    position: relative;
    height: calc(70vh - 44px);
    // border: 1px solid blue;
    overflow: auto;

    .content{
        // border: 1px solid blue;

    }
  }

</style>
